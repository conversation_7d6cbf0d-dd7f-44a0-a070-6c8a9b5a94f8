// Instagram早期脚本 - 完全对齐efluns的架构
// runAt: document_start, world: MAIN

import './instagram-early.css';

// 检查是否在Instagram页面
function isInstagramPage(): boolean {
  return window.location.hostname === 'www.instagram.com';
}

// 发送事件的辅助函数
function sendEvent(name: string, data: any) {
  window.dispatchEvent(new CustomEvent(name, { detail: data }));
}

// 主函数
function main() {
  if (!isInstagramPage()) return;
  
  console.log('Instagram Early Script is loading');

  // 拦截fetch请求 - 完全按照efluns的方式
  const originalFetch = window.fetch;
  window.fetch = async function(...args) {
    const [resource] = args;
    // 调用原始fetch
    const response = await originalFetch.apply(this, args);
   
    // 专门拦截包含用户详细信息的请求
    if(typeof resource === 'string' && resource.includes('/async/wbloks/fetch/')){
      const clonedResponse = response.clone();

      try {
        // 1. 移除开头的 "for (;;);" 安全前缀
        const rawResponse = await clonedResponse.text();
        const jsonStr = rawResponse.replace(/^for \(;;\);/, '');

        // 2. 解析JSON
        const data = JSON.parse(jsonStr);
        
        // 3. 提取bloks_payload中的地区信息
        const bloksPayload = data?.payload?.layout?.bloks_payload;
        
        if (bloksPayload?.data?.[0]?.data?.initial) {
          console.log('Instagram地区信息获取成功:', bloksPayload.data[0].data.initial);
          // 发送地区信息事件 - 完全按照efluns的方式
          sendEvent("INS_AREA", bloksPayload.data[0].data.initial);
        }
      } catch (error) {
        console.error('解析Instagram地区信息时出错:', error);
      }
    }
    
    return response;
  };

  console.log('Instagram网络拦截器设置完成');
}

// 立即执行
main();
