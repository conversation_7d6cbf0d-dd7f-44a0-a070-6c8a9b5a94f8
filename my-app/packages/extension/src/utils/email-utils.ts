/**
 * Instagram 邮箱获取工具函数
 * 参考 efluns 插件实现，支持多层次邮箱获取
 */

// 邮箱正则表达式 - 使用更严格的规则
const EMAIL_REGEX = /[a-zA-Z0-9][a-zA-Z0-9._%+-]*@([a-zA-Z0-9][a-zA-Z0-9-]*\.){1,}[a-zA-Z]+/;
const COMMON_EMAIL_REGEX = /\b[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Z|a-z]{2,7}\b/;

// Instagram 描述选择器
const INS_DESCRIPTION_SELECTOR = 'header section:nth-child(3) > div > span';

/**
 * 从文本中提取邮箱地址
 * @param text 要搜索的文本
 * @returns 提取到的邮箱地址或null
 */
export function extractEmailFromText(text: string): string | null {
  if (!text) return null;
  
  const matches = text.match(EMAIL_REGEX);
  
  if (!matches || matches.length === 0) {
    return null;
  }

  // 检查邮箱后面是否跟着省略号
  const email = matches[0];
  const emailIndex = text.indexOf(email);
  const nextChar = text[emailIndex + email.length];
  
  // 如果邮箱后面跟着省略号，认为邮箱不完整
  if (nextChar === '.') {
    return null;
  }

  return email;
}

/**
 * 获取Instagram用户描述文本
 * @returns 用户描述文本或null
 */
export function getInstagramDescriptionText(): string | null {
  try {
    const descriptionElement = document.querySelector(INS_DESCRIPTION_SELECTOR) as HTMLElement;
    return descriptionElement?.innerText || null;
  } catch (error) {
    console.error('获取Instagram描述文本失败:', error);
    return null;
  }
}

/**
 * 从Instagram描述中获取邮箱
 * @returns 提取到的邮箱地址或null
 */
export function getEmailFromDescription(): string | null {
  const descriptionText = getInstagramDescriptionText();
  return extractEmailFromText(descriptionText || '');
}

/**
 * 标记页面中包含邮箱的元素
 * @returns 包含邮箱的元素数组
 */
export function markAllContainedEmailElements(): HTMLElement[] {
  const elementsWithEmail: HTMLElement[] = [];
  
  try {
    // 获取所有非script元素
    const allElements = document.querySelectorAll<HTMLElement>('*:not(script)');
    
    for (const element of allElements) {
      const childNodes = Array.from(element.childNodes);
      
      // 检查是否存在直接的文本节点包含邮箱地址
      for (const node of childNodes) {
        if (node.nodeType === Node.TEXT_NODE) {
          const matches = node.textContent?.trim().match(COMMON_EMAIL_REGEX);
          const email = matches?.[0];
          
          if (email) {
            // 将包含邮箱地址的元素添加到列表中
            elementsWithEmail.push(element);
            // 给元素添加自定义属性
            element.setAttribute('data-email-anchor', 'true');
            element.setAttribute('data-email', email);
            break; // 找到一个邮箱就跳出内层循环
          }
        }
      }
    }
  } catch (error) {
    console.error('标记邮箱元素失败:', error);
  }
  
  return elementsWithEmail;
}

/**
 * 从DOM中扫描邮箱地址
 * @returns 扫描到的邮箱地址数组
 */
export function scanEmailsFromDOM(): string[] {
  const emails: Set<string> = new Set();
  
  try {
    const elementsWithEmail = markAllContainedEmailElements();
    
    elementsWithEmail.forEach(element => {
      const email = element.getAttribute('data-email');
      if (email) {
        emails.add(email);
      }
    });
  } catch (error) {
    console.error('DOM邮箱扫描失败:', error);
  }
  
  return Array.from(emails);
}

/**
 * 从用户简介中获取邮箱（通过网络请求拦截）
 * 这个函数会监听Instagram的网络请求来获取用户详细信息
 * @returns Promise<string | null>
 */
export function getEmailFromProfile(): Promise<string | null> {
  return new Promise((resolve) => {
    // 设置超时
    const timeout = setTimeout(() => {
      window.removeEventListener('INS_PROFILE_DATA', handleProfileData as EventListener);
      resolve(null);
    }, 5000);

    // 监听用户详细信息事件
    const handleProfileData = (event: CustomEvent) => {
      try {
        const data = event.detail;

        // 从用户数据中提取邮箱
        let email = null;

        // 尝试从不同字段获取邮箱
        if (data.email) {
          email = data.email;
        } else if (data.contact_email) {
          email = data.contact_email;
        } else if (data.business_email) {
          email = data.business_email;
        } else if (data.public_email) {
          email = data.public_email;
        }

        // 验证邮箱格式
        if (email && EMAIL_REGEX.test(email)) {
          clearTimeout(timeout);
          window.removeEventListener('INS_PROFILE_DATA', handleProfileData as EventListener);
          resolve(email);
        }
      } catch (error) {
        console.error('处理用户简介数据失败:', error);
      }
    };

    // 监听用户简介数据事件
    window.addEventListener('INS_PROFILE_DATA', handleProfileData as EventListener);
  });
}

/**
 * 多层次获取邮箱地址
 * 按优先级从描述、DOM扫描、用户简介中获取邮箱
 * @returns Promise<string | null>
 */
export async function getEmailMultiLevel(): Promise<string | null> {
  console.log('开始多层次邮箱获取...');
  
  // 1. 首先从描述中获取
  const emailFromDescription = getEmailFromDescription();
  if (emailFromDescription) {
    console.log('从描述中获取到邮箱:', emailFromDescription);
    return emailFromDescription;
  }
  
  // 2. 从DOM扫描获取
  const emailsFromDOM = scanEmailsFromDOM();
  if (emailsFromDOM.length > 0) {
    console.log('从DOM扫描获取到邮箱:', emailsFromDOM[0]);
    return emailsFromDOM[0];
  }
  
  // 3. 从用户简介获取
  try {
    const emailFromProfile = await getEmailFromProfile();
    if (emailFromProfile) {
      console.log('从用户简介获取到邮箱:', emailFromProfile);
      return emailFromProfile;
    }
  } catch (error) {
    console.error('从用户简介获取邮箱失败:', error);
  }
  
  console.log('未能获取到邮箱地址');
  return null;
}

/**
 * 复制文本到剪贴板
 * @param text 要复制的文本
 * @returns Promise<boolean> 复制是否成功
 */
export async function copyToClipboard(text: string): Promise<boolean> {
  try {
    if (navigator.clipboard && window.isSecureContext) {
      await navigator.clipboard.writeText(text);
      return true;
    } else {
      // 备用方案：使用传统的复制方法
      const textArea = document.createElement('textarea');
      textArea.value = text;
      textArea.style.position = 'fixed';
      textArea.style.left = '-999999px';
      textArea.style.top = '-999999px';
      document.body.appendChild(textArea);
      textArea.focus();
      textArea.select();
      
      const result = document.execCommand('copy');
      document.body.removeChild(textArea);
      return result;
    }
  } catch (error) {
    console.error('复制到剪贴板失败:', error);
    return false;
  }
}

/**
 * 验证邮箱格式
 * @param email 邮箱地址
 * @returns 是否为有效邮箱
 */
export function isValidEmail(email: string): boolean {
  return EMAIL_REGEX.test(email);
}
