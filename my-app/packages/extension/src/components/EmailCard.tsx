import React, { useState } from 'react';
import { copyToClipboard } from '../utils/email-utils';

interface EmailCardProps {
  email: string;
  onCopy?: (email: string) => void;
}

export const EmailCard: React.FC<EmailCardProps> = ({ email, onCopy }) => {
  const [copied, setCopied] = useState(false);
  const [isHovered, setIsHovered] = useState(false);

  const handleCopy = async () => {
    const success = await copyToClipboard(email);
    if (success) {
      setCopied(true);
      onCopy?.(email);
      
      // 2秒后重置复制状态
      setTimeout(() => {
        setCopied(false);
      }, 2000);
    }
  };

  // 分割邮箱显示
  const emailParts = email.split('@');
  const username = emailParts[0];
  const domain = emailParts[1];

  return (
    <div
      className="email-card"
      onClick={handleCopy}
      onMouseEnter={() => setIsHovered(true)}
      onMouseLeave={() => setIsHovered(false)}
      style={{
        display: 'flex',
        alignItems: 'center',
        padding: '8px 12px',
        backgroundColor: isHovered ? '#f0f8ff' : '#ffffff',
        border: '1px solid #e1e5e9',
        borderRadius: '8px',
        cursor: 'pointer',
        transition: 'all 0.2s ease',
        boxShadow: isHovered ? '0 2px 8px rgba(0,0,0,0.1)' : '0 1px 3px rgba(0,0,0,0.05)',
        marginBottom: '8px',
        minWidth: '200px',
        maxWidth: '300px',
      }}
    >
      {/* 邮箱图标 */}
      <svg
        width="20"
        height="20"
        viewBox="0 0 24 24"
        fill="none"
        xmlns="http://www.w3.org/2000/svg"
        style={{ marginRight: '8px', flexShrink: 0 }}
      >
        <path
          d="M2 5v14h20V5H2zm19 1v.88l-9 6.8-9-6.8V6h18zM3 18V8.13l9 6.8 9-6.8V18H3z"
          fill="#666"
        />
      </svg>

      {/* 邮箱地址 */}
      <div style={{ 
        flex: 1, 
        overflow: 'hidden',
        fontSize: '14px',
        color: '#333'
      }}>
        <div style={{ 
          display: 'flex',
          alignItems: 'center',
          flexWrap: 'wrap'
        }}>
          <span style={{ 
            fontWeight: '500',
            color: '#1a73e8'
          }}>
            {username}
          </span>
          <span style={{ color: '#666' }}>@</span>
          <span style={{ color: '#666' }}>{domain}</span>
        </div>
      </div>

      {/* 复制状态指示 */}
      <div style={{ 
        marginLeft: '8px',
        fontSize: '12px',
        color: copied ? '#4caf50' : '#999',
        fontWeight: '500',
        minWidth: '40px',
        textAlign: 'center'
      }}>
        {copied ? '✓ 已复制' : '点击复制'}
      </div>
    </div>
  );
};

interface EmailSectionProps {
  emails: string[];
  title?: string;
  onEmailCopy?: (email: string) => void;
}

export const EmailSection: React.FC<EmailSectionProps> = ({ 
  emails, 
  title = "邮箱地址",
  onEmailCopy 
}) => {
  if (!emails || emails.length === 0) {
    return null;
  }

  return (
    <div style={{
      backgroundColor: '#ffffff',
      border: '1px solid #e1e5e9',
      borderRadius: '12px',
      padding: '16px',
      marginBottom: '12px',
      boxShadow: '0 2px 8px rgba(0,0,0,0.05)',
    }}>
      {/* 标题 */}
      <div style={{
        fontSize: '16px',
        fontWeight: '600',
        color: '#333',
        marginBottom: '12px',
        display: 'flex',
        alignItems: 'center',
      }}>
        <svg
          width="18"
          height="18"
          viewBox="0 0 24 24"
          fill="none"
          xmlns="http://www.w3.org/2000/svg"
          style={{ marginRight: '6px' }}
        >
          <path
            d="M2 5v14h20V5H2zm19 1v.88l-9 6.8-9-6.8V6h18zM3 18V8.13l9 6.8 9-6.8V18H3z"
            fill="#1a73e8"
          />
        </svg>
        {title}
        {emails.length > 1 && (
          <span style={{
            marginLeft: '8px',
            fontSize: '12px',
            backgroundColor: '#e3f2fd',
            color: '#1976d2',
            padding: '2px 6px',
            borderRadius: '10px',
            fontWeight: '500'
          }}>
            {emails.length}个
          </span>
        )}
      </div>

      {/* 邮箱列表 */}
      <div>
        {emails.map((email, index) => (
          <EmailCard
            key={`${email}-${index}`}
            email={email}
            onCopy={onEmailCopy}
          />
        ))}
      </div>

      {/* 提示信息 */}
      <div style={{
        fontSize: '12px',
        color: '#666',
        marginTop: '8px',
        textAlign: 'center',
        fontStyle: 'italic'
      }}>
        ⓘ 点击邮箱地址即可复制到剪贴板
      </div>
    </div>
  );
};
