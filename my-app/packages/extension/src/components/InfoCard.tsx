
import React, { useState, useEffect } from 'react';
import { UserInfoCard } from './RegionFloatingWindow';
import { isInInsKOLPage, extractInsKOLHandler } from '../utils/dom-utils';
import { getUserAreaFromIntroCache, setUserAreaFromIntroCache, getUserAreaFromIntro } from '../utils/getUserAreaFromIntro';
import { getEmailMultiLevel, isValidEmail } from '../utils/email-utils';
import { listenEvent } from '../utils/event-system';
import './InfoCard.css';

interface RegionInfo {
  region: string;
}

interface EmailInfo {
  emails: string[];
  lastUpdated: number;
}

export const InfoCard: React.FC = () => {
  const [regionInfo, setRegionInfo] = useState<RegionInfo | null>(null);
  const [emailInfo, setEmailInfo] = useState<EmailInfo | null>(null);
  const [currentUrl, setCurrentUrl] = useState(window.location.href);
  const [autoTriggerEnabled] = useState(true); // 简单的开关控制
  const [isLoadingEmail, setIsLoadingEmail] = useState(false);

  useEffect(() => {
    let unlistenEvent: (() => void) | null = null;

    // 邮箱获取逻辑
    const loadEmailInfo = async () => {
      setIsLoadingEmail(true);
      try {
        console.log('开始获取邮箱信息...');
        const email = await getEmailMultiLevel();

        if (email && isValidEmail(email)) {
          console.log('成功获取邮箱:', email);
          setEmailInfo({
            emails: [email],
            lastUpdated: Date.now()
          });
        } else {
          console.log('未获取到有效邮箱');
          setEmailInfo(null);
        }
      } catch (error) {
        console.error('获取邮箱失败:', error);
        setEmailInfo(null);
      } finally {
        setIsLoadingEmail(false);
      }
    };

    const checkAndLoadRegion = () => {
      // 只在Instagram KOL页面显示
      if (!isInInsKOLPage()) {
        setRegionInfo(null);
        setEmailInfo(null);
        return;
      }

      const handler = extractInsKOLHandler(window.location.href);
      if (!handler) return;

      // 检查缓存
      const cachedRegion = getUserAreaFromIntroCache(handler);
      if (cachedRegion) {
        setRegionInfo({ region: cachedRegion });
      }

      // 监听地区数据事件
      if (unlistenEvent) unlistenEvent();

      unlistenEvent = listenEvent('INS_AREA', (data: string) => {
        if (data && typeof data === 'string') {
          setRegionInfo({ region: data });
          setUserAreaFromIntroCache(handler, data);
        }
      });

      // 完全按照efluns的逻辑：只有在特定条件下才触发获取地区信息
      // 添加条件控制，避免过度触发
      if (autoTriggerEnabled) {
        setTimeout(() => {
          getUserAreaFromIntro().catch(console.error);
        }, 1000);

        // 同时获取邮箱信息
        setTimeout(() => {
          loadEmailInfo();
        }, 1500);
      }
    };

    // 监听URL变化 - 借鉴efluns的方法
    const handleUrlChange = () => {
      const newUrl = window.location.href;
      if (newUrl !== currentUrl) {
        setCurrentUrl(newUrl);
        // 清理当前状态
        setRegionInfo(null);
        setEmailInfo(null);
        setIsLoadingEmail(false);
        if (unlistenEvent) {
          unlistenEvent();
          unlistenEvent = null;
        }
        // 延迟检查新页面
        setTimeout(checkAndLoadRegion, 800);
      }
    };

    // 初始检查
    checkAndLoadRegion();

    // 使用现代Navigation API（如果支持）
    if ('navigation' in window) {
      (window as any).navigation.addEventListener('navigatesuccess', handleUrlChange);
    }

    // 备用方案：监听popstate和定期检查
    window.addEventListener('popstate', handleUrlChange);
    const intervalId = setInterval(handleUrlChange, 500);

    return () => {
      if ('navigation' in window) {
        (window as any).navigation.removeEventListener('navigatesuccess', handleUrlChange);
      }
      window.removeEventListener('popstate', handleUrlChange);
      clearInterval(intervalId);
      if (unlistenEvent) unlistenEvent();
    };
  }, [currentUrl]);

  // 邮箱复制处理函数
  const handleEmailCopy = (email: string) => {
    console.log('邮箱已复制:', email);
    // 这里可以添加复制成功的提示
  };

  // 只在有地区信息、邮箱信息或加载状态时显示
  if (!regionInfo && !emailInfo && !isLoadingEmail) return null;

  return (
    <div
      className="info-card-container"
      style={{
        position: 'fixed',
        top: '20px',
        right: '20px',
        zIndex: 999999,
        maxWidth: '280px',
        width: 'auto',
        // 确保不会超出屏幕边界
        maxHeight: 'calc(100vh - 40px)',
        overflow: 'auto'
      }}
    >
      <UserInfoCard
        regionInfo={regionInfo}
        emailInfo={emailInfo}
        isLoadingEmail={isLoadingEmail}
        onEmailCopy={handleEmailCopy}
      />
    </div>
  );
};
