/* InfoCard 组件样式 */

@keyframes loading {
  0% {
    transform: translateX(-100%);
  }
  50% {
    transform: translateX(0%);
  }
  100% {
    transform: translateX(100%);
  }
}

.email-card {
  transition: all 0.2s ease;
}

.email-card:hover {
  transform: translateY(-1px);
}

.email-card:active {
  transform: translateY(0px);
}

/* 响应式设计 */
@media (max-width: 768px) {
  .info-card-container {
    top: 10px !important;
    right: 10px !important;
    max-width: 260px !important;
  }
}

@media (max-width: 480px) {
  .info-card-container {
    top: 10px !important;
    right: 10px !important;
    left: 10px !important;
    max-width: none !important;
    width: calc(100vw - 20px) !important;
  }
}

/* 确保在Instagram页面上的层级 */
.info-card-container {
  z-index: 999999 !important;
}

/* 邮箱卡片动画 */
.email-section {
  animation: slideInRight 0.3s ease-out;
}

@keyframes slideInRight {
  from {
    opacity: 0;
    transform: translateX(20px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

/* 加载动画 */
.loading-container {
  animation: fadeIn 0.2s ease-in;
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}
