/**
 * 综合用户信息显示组件
 * 显示地区信息和邮箱信息，简洁美观
 */

import React, { useState } from 'react';
import { getCountryCodeFromName, getCountryIcon } from '../utils/instagram-utils';
import { copyToClipboard } from '../utils/email-utils';

interface RegionInfo {
  region: string;
}

interface EmailInfo {
  emails: string[];
  lastUpdated: number;
}

interface UserInfoCardProps {
  regionInfo?: RegionInfo | null;
  emailInfo?: EmailInfo | null;
  isLoadingEmail?: boolean;
  onEmailCopy?: (email: string) => void;
}

export const UserInfoCard: React.FC<UserInfoCardProps> = ({
  regionInfo,
  emailInfo,
  isLoadingEmail,
  onEmailCopy
}) => {
  // 复制状态管理
  const [copiedEmail, setCopiedEmail] = useState<string | null>(null);

  // 邮箱复制处理
  const handleEmailCopy = async (email: string) => {
    try {
      await copyToClipboard(email);
      setCopiedEmail(email);
      onEmailCopy?.(email);

      // 2秒后清除复制状态
      setTimeout(() => {
        setCopiedEmail(null);
      }, 2000);
    } catch (error) {
      console.error('复制邮箱失败:', error);
    }
  };

  // 如果没有任何信息，不显示组件
  if (!regionInfo && !emailInfo && !isLoadingEmail) {
    return null;
  }

  return (
    <div style={{
      position: 'relative',
      zIndex: 999999,
      background: 'rgba(255, 255, 255, 0.95)',
      backdropFilter: 'blur(10px)',
      borderRadius: '12px',
      boxShadow: '0 8px 32px rgba(0, 0, 0, 0.1)',
      border: '1px solid rgba(255, 255, 255, 0.2)',
      padding: '16px',
      animation: 'slideIn 0.3s ease-out',
      fontFamily: '-apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif',
      fontSize: '14px',
      minWidth: '200px',
      maxWidth: '280px',
      width: 'fit-content',
      margin: '0 auto',
      wordBreak: 'break-all',
      overflow: 'hidden'
    }}>

      {/* 地区信息部分 */}
      {regionInfo && (
        <div style={{ marginBottom: emailInfo || isLoadingEmail ? '16px' : '0' }}>
          {/* 地区标题 */}
          <div style={{
            display: 'flex',
            alignItems: 'center',
            gap: '6px',
            marginBottom: '8px',
            color: '#6b7280',
            fontSize: '12px',
            fontWeight: '500'
          }}>
            <svg width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
              <path d="M21 10c0 7-9 13-9 13s-9-6-9-13a9 9 0 0 1 18 0z"></path>
              <circle cx="12" cy="10" r="3"></circle>
            </svg>
            账户所在地
          </div>

          {/* 地区信息 */}
          <div style={{
            display: 'flex',
            alignItems: 'center',
            gap: '8px'
          }}>
            {(() => {
              const countryCode = getCountryCodeFromName(regionInfo.region);
              const flagUrl = getCountryIcon(countryCode);
              return (
                <>
                  {flagUrl && (
                    <img
                      src={flagUrl}
                      alt={regionInfo.region}
                      style={{
                        width: '20px',
                        height: '15px',
                        borderRadius: '2px',
                        border: '1px solid rgba(0, 0, 0, 0.1)',
                        flexShrink: 0
                      }}
                    />
                  )}
                  <span style={{
                    color: '#1f2937',
                    fontWeight: '600',
                    fontSize: '14px'
                  }}>
                    {regionInfo.region}
                  </span>
                </>
              );
            })()}
          </div>
        </div>
      )}

      {/* 邮箱信息部分 */}
      {emailInfo && emailInfo.emails.length > 0 && (
        <div>
          {/* 邮箱标题 */}
          <div style={{
            display: 'flex',
            alignItems: 'center',
            gap: '6px',
            marginBottom: '8px',
            color: '#6b7280',
            fontSize: '12px',
            fontWeight: '500'
          }}>
            <svg width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
              <path d="M4 4h16c1.1 0 2 .9 2 2v12c0 1.1-.9 2-2 2H4c-1.1 0-2-.9-2-2V6c0-1.1.9-2 2-2z"></path>
              <polyline points="22,6 12,13 2,6"></polyline>
            </svg>
            联系邮箱
          </div>

          {/* 邮箱列表 */}
          <div style={{ display: 'flex', flexDirection: 'column', gap: '6px' }}>
            {emailInfo.emails.map((email, index) => {
              const [username, domain] = email.split('@');
              const isCopied = copiedEmail === email;

              return (
                <div
                  key={index}
                  onClick={() => handleEmailCopy(email)}
                  style={{
                    display: 'flex',
                    alignItems: 'center',
                    gap: '4px',
                    padding: '8px 12px',
                    backgroundColor: isCopied ? '#dcfce7' : '#f8fafc',
                    border: `1px solid ${isCopied ? '#16a34a' : '#e2e8f0'}`,
                    borderRadius: '8px',
                    cursor: 'pointer',
                    transition: 'all 0.2s ease',
                    fontSize: '12px',
                    fontFamily: 'Monaco, "Cascadia Code", "Roboto Mono", monospace',
                    maxWidth: '100%',
                    overflow: 'hidden',
                    position: 'relative'
                  }}
                  onMouseEnter={(e) => {
                    if (!isCopied) {
                      e.currentTarget.style.backgroundColor = '#e2e8f0';
                      e.currentTarget.style.transform = 'translateY(-1px)';
                      e.currentTarget.style.boxShadow = '0 2px 8px rgba(0,0,0,0.1)';
                    }
                  }}
                  onMouseLeave={(e) => {
                    if (!isCopied) {
                      e.currentTarget.style.backgroundColor = '#f8fafc';
                      e.currentTarget.style.transform = 'translateY(0px)';
                      e.currentTarget.style.boxShadow = 'none';
                    }
                  }}
                >
                  {/* 邮箱内容 */}
                  <div style={{
                    display: 'flex',
                    alignItems: 'center',
                    gap: '2px',
                    flex: 1,
                    minWidth: 0,
                    overflow: 'hidden'
                  }}>
                    <span style={{
                      color: '#3b82f6',
                      fontWeight: '600',
                      overflow: 'hidden',
                      textOverflow: 'ellipsis',
                      whiteSpace: 'nowrap',
                      maxWidth: '80px'
                    }}>
                      {username}
                    </span>
                    <span style={{ color: '#6b7280', flexShrink: 0 }}>@</span>
                    <span style={{
                      color: '#059669',
                      fontWeight: '500',
                      overflow: 'hidden',
                      textOverflow: 'ellipsis',
                      whiteSpace: 'nowrap',
                      maxWidth: '100px'
                    }}>
                      {domain}
                    </span>
                  </div>

                  {/* 状态图标 */}
                  <div style={{
                    marginLeft: 'auto',
                    opacity: isCopied ? 1 : 0.6,
                    flexShrink: 0,
                    display: 'flex',
                    alignItems: 'center',
                    gap: '4px'
                  }}>
                    {isCopied ? (
                      <>
                        <span style={{
                          fontSize: '10px',
                          color: '#16a34a',
                          fontWeight: '600',
                          whiteSpace: 'nowrap'
                        }}>
                          Copied
                        </span>
                        <svg width="12" height="12" viewBox="0 0 24 24" fill="none" stroke="#16a34a" strokeWidth="2">
                          <polyline points="20,6 9,17 4,12"></polyline>
                        </svg>
                      </>
                    ) : (
                      <svg width="12" height="12" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                        <rect x="9" y="9" width="13" height="13" rx="2" ry="2"></rect>
                        <path d="m5 15-4-4 4-4"></path>
                      </svg>
                    )}
                  </div>
                </div>
              );
            })}
          </div>
        </div>
      )}

      {/* 邮箱加载状态 */}
      {isLoadingEmail && (
        <div>
          {/* 加载标题 */}
          <div style={{
            display: 'flex',
            alignItems: 'center',
            gap: '6px',
            marginBottom: '8px',
            color: '#6b7280',
            fontSize: '12px',
            fontWeight: '500'
          }}>
            <svg width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
              <path d="M4 4h16c1.1 0 2 .9 2 2v12c0 1.1-.9 2-2 2H4c-1.1 0-2-.9-2-2V6c0-1.1.9-2 2-2z"></path>
              <polyline points="22,6 12,13 2,6"></polyline>
            </svg>
            联系邮箱
          </div>

          {/* 加载动画 */}
          <div style={{
            padding: '12px',
            backgroundColor: '#f8fafc',
            border: '1px solid #e2e8f0',
            borderRadius: '8px',
            textAlign: 'center',
            fontSize: '13px',
            color: '#6b7280'
          }}>
            <div style={{ marginBottom: '8px' }}>🔍 正在搜索邮箱...</div>
            <div style={{
              width: '100%',
              height: '3px',
              backgroundColor: '#e2e8f0',
              borderRadius: '2px',
              overflow: 'hidden'
            }}>
              <div style={{
                width: '30%',
                height: '100%',
                backgroundColor: '#3b82f6',
                borderRadius: '2px',
                animation: 'loading 1.5s ease-in-out infinite'
              }} />
            </div>
          </div>
        </div>
      )}

      <style>{`
        @keyframes slideIn {
          from {
            opacity: 0;
            transform: translateX(20px);
          }
          to {
            opacity: 1;
            transform: translateX(0);
          }
        }

        @keyframes loading {
          0% {
            transform: translateX(-100%);
          }
          50% {
            transform: translateX(0%);
          }
          100% {
            transform: translateX(100%);
          }
        }
      `}</style>
    </div>
  );
};

// 保持向后兼容性
export const RegionFloatingWindow = UserInfoCard;
