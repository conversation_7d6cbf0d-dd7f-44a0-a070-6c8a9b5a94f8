# Instagram 邮箱获取功能

## 功能概述

本功能为 my-app 扩展程序添加了 Instagram 邮箱获取能力，能够从多个维度自动提取 Instagram 用户的邮箱地址，并在信息卡中展示，支持一键复制。

## 功能特点

### 🔍 多层次邮箱获取
1. **页面描述提取** - 从用户简介描述文本中提取邮箱
2. **DOM 扫描** - 扫描页面所有元素，查找包含邮箱的文本节点
3. **用户简介获取** - 通过网络请求拦截获取用户详细信息中的邮箱

### 📧 智能邮箱处理
- 使用严格的邮箱正则表达式验证
- 过滤不完整的邮箱（如带省略号的邮箱）
- 支持多种邮箱字段（email, contact_email, business_email, public_email）

### 🎨 用户友好界面
- 美观的邮箱卡片设计
- 分割显示用户名和域名
- 悬停效果和点击反馈
- 复制成功状态提示

### ⚡ 性能优化
- 缓存机制避免重复获取
- 异步加载不阻塞页面
- 智能超时处理

## 技术实现

### 核心文件

1. **utils/email-utils.ts** - 邮箱获取核心逻辑
   - `extractEmailFromText()` - 文本邮箱提取
   - `getEmailFromDescription()` - 描述邮箱获取
   - `scanEmailsFromDOM()` - DOM 邮箱扫描
   - `getEmailFromProfile()` - 用户简介邮箱获取
   - `getEmailMultiLevel()` - 多层次邮箱获取
   - `copyToClipboard()` - 剪贴板复制

2. **components/EmailCard.tsx** - 邮箱显示组件
   - `EmailCard` - 单个邮箱卡片
   - `EmailSection` - 邮箱区域容器

3. **components/InfoCard.tsx** - 主信息卡组件
   - 集成邮箱获取逻辑
   - 状态管理和事件处理

4. **content/instagram-early-script.ts** - 网络拦截脚本
   - 拦截 Instagram API 请求
   - 提取用户详细信息

### 工作流程

```
1. 用户访问 Instagram KOL 页面
   ↓
2. InfoCard 组件检测页面类型
   ↓
3. 触发多层次邮箱获取
   ↓
4. 按优先级获取邮箱：
   - 描述文本 → DOM扫描 → 用户简介
   ↓
5. 验证邮箱格式
   ↓
6. 在信息卡中显示邮箱
   ↓
7. 用户点击复制到剪贴板
```

## 使用方法

### 安装扩展
1. 构建项目：`npm run build`
2. 在 Chrome 中加载 `dist` 文件夹
3. 访问 Instagram KOL 页面

### 使用功能
1. 打开任意 Instagram 用户页面
2. 等待信息卡出现在页面右上角
3. 如果检测到邮箱，会显示"联系邮箱"区域
4. 点击邮箱地址即可复制到剪贴板

## 配置选项

### 邮箱正则表达式
```typescript
const EMAIL_REGEX = /[a-zA-Z0-9][a-zA-Z0-9._%+-]*@([a-zA-Z0-9][a-zA-Z0-9-]*\.){1,}[a-zA-Z]+/;
```

### 超时设置
- 用户简介获取超时：5秒
- 邮箱获取延迟：1.5秒

### 样式配置
- 信息卡位置：右上角 (top: 20px, right: 20px)
- 最大宽度：320px
- 响应式断点：768px

## 调试信息

### 控制台日志
- `开始获取邮箱信息...` - 开始邮箱获取流程
- `从描述中获取到邮箱: xxx` - 描述提取成功
- `从DOM扫描获取到邮箱: xxx` - DOM扫描成功
- `从用户简介获取到邮箱: xxx` - 简介获取成功
- `未能获取到邮箱地址` - 所有方法都失败

### 网络拦截
- 监听 `/async/wbloks/fetch/` - 地区信息
- 监听 `/api/v1/users/`, `/graphql`, `/web/profile_info/` - 用户信息

## 故障排除

### 常见问题

1. **邮箱未显示**
   - 检查是否在 Instagram KOL 页面
   - 查看控制台是否有错误日志
   - 确认用户是否公开了邮箱信息

2. **复制功能不工作**
   - 检查浏览器是否支持 Clipboard API
   - 确认页面是 HTTPS 协议
   - 查看是否有权限限制

3. **样式显示异常**
   - 检查 CSS 文件是否正确加载
   - 确认没有与 Instagram 样式冲突
   - 验证 z-index 层级设置

### 性能优化建议

1. 避免频繁触发邮箱获取
2. 使用缓存减少重复请求
3. 合理设置超时时间
4. 优化正则表达式性能

## 更新日志

### v1.0.0 (2024-08-03)
- ✨ 新增多层次邮箱获取功能
- ✨ 新增邮箱显示和复制功能
- ✨ 新增网络请求拦截
- ✨ 新增智能邮箱验证
- 🎨 优化用户界面设计
- 🐛 修复 TypeScript 类型错误

## 技术栈

- **TypeScript** - 类型安全的 JavaScript
- **React** - 用户界面库
- **Webpack** - 模块打包工具
- **CSS3** - 样式和动画
- **Chrome Extension API** - 浏览器扩展接口

## 贡献指南

1. Fork 项目
2. 创建功能分支
3. 提交更改
4. 推送到分支
5. 创建 Pull Request

## 许可证

本项目采用 MIT 许可证。
